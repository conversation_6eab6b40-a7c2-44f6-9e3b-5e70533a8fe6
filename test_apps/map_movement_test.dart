import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../lib/utils/map_coordinate_utils.dart';
import '../lib/utils/tianditu_utils.dart';

/// 地图移动测试应用
/// 用于验证地图移动方向是否正确
class MapMovementTestApp extends StatelessWidget {
  const MapMovementTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '地图移动测试',
      home: const MapMovementTestPage(),
    );
  }
}

class MapMovementTestPage extends StatefulWidget {
  const MapMovementTestPage({super.key});

  @override
  State<MapMovementTestPage> createState() => _MapMovementTestPageState();
}

class _MapMovementTestPageState extends State<MapMovementTestPage> {
  final MapController mapController = MapController();
  LatLng centerCoordinate = const LatLng(39.9042, 116.4074); // 北京
  bool showCenterMarker = true;
  bool showTargetMarker = false;
  LatLng? targetCoordinate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('地图移动测试'),
        backgroundColor: Colors.blue,
      ),
      body: Stack(
        children: [
          // 地图
          FlutterMap(
            mapController: mapController,
            options: MapOptions(
              initialCenter: centerCoordinate,
              initialZoom: 15.0,
              onPositionChanged: (position, hasGesture) {
                if (hasGesture) {
                  setState(() {
                    centerCoordinate = position.center!;
                  });
                }
              },
            ),
            children: [
              // 底图层
              TileLayer(
                urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                  isVector: true,
                  isAnnotation: false,
                ),
                additionalOptions: {'k': TianDiTuUtils.key},
                subdomains: TianDiTuUtils.subdomains,
                userAgentPackageName: 'com.example.fishing_app',
              ),
              
              // 注记层
              TileLayer(
                urlTemplate: TianDiTuUtils.buildTileUrlTemplate(
                  isVector: true,
                  isAnnotation: true,
                ),
                additionalOptions: {'k': TianDiTuUtils.key},
                subdomains: TianDiTuUtils.subdomains,
                userAgentPackageName: 'com.example.fishing_app',
              ),

              // 标记层
              MarkerLayer(
                markers: [
                  // 中心标记
                  if (showCenterMarker)
                    Marker(
                      point: centerCoordinate,
                      width: 40,
                      height: 40,
                      child: const Icon(
                        Icons.center_focus_strong,
                        color: Colors.red,
                        size: 40,
                      ),
                    ),
                  
                  // 目标标记
                  if (showTargetMarker && targetCoordinate != null)
                    Marker(
                      point: targetCoordinate!,
                      width: 40,
                      height: 40,
                      child: const Icon(
                        Icons.location_pin,
                        color: Colors.green,
                        size: 40,
                      ),
                    ),
                ],
              ),
            ],
          ),

          // 屏幕25%高度处的参考线
          Positioned(
            left: 0,
            right: 0,
            top: MediaQuery.of(context).size.height * 0.25,
            child: Container(
              height: 2,
              color: Colors.orange.withOpacity(0.7),
            ),
          ),

          // 屏幕中心的参考线
          Positioned(
            left: 0,
            right: 0,
            top: MediaQuery.of(context).size.height * 0.5,
            child: Container(
              height: 2,
              color: Colors.blue.withOpacity(0.7),
            ),
          ),

          // 控制面板
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '当前中心坐标:\n${centerCoordinate.latitude.toStringAsFixed(6)}, ${centerCoordinate.longitude.toStringAsFixed(6)}',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 12),
                    ),
                    const SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: _testMoveToTop,
                          child: const Text('移动到25%'),
                        ),
                        ElevatedButton(
                          onPressed: _resetToCenter,
                          child: const Text('重置中心'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Text(
                      '说明：\n蓝线=屏幕中心(50%)\n橙线=目标位置(25%)\n红色图标=地图中心\n绿色图标=目标坐标',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 测试移动到25%高度处
  void _testMoveToTop() {
    final screenSize = MediaQuery.of(context).size;
    
    // 记录当前中心坐标作为目标坐标
    setState(() {
      targetCoordinate = centerCoordinate;
      showTargetMarker = true;
    });

    // 使用工具类移动地图
    MapCoordinateUtils.moveCoordinateToScreenPosition(
      mapController,
      centerCoordinate,
      screenHeightPercent: 0.25,
      screenWidthPercent: 0.50,
      screenSize: screenSize,
    );

    // 显示提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已将当前中心坐标移动到25%高度处'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// 重置到中心位置
  void _resetToCenter() {
    setState(() {
      centerCoordinate = const LatLng(39.9042, 116.4074);
      showTargetMarker = false;
      targetCoordinate = null;
    });
    
    mapController.move(centerCoordinate, 15.0);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已重置到北京中心位置'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

void main() {
  runApp(const MapMovementTestApp());
}
