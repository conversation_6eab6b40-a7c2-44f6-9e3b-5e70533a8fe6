import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/utils/marker_alignment_utils.dart';

void main() {
  group('MarkerAlignmentUtils Tests', () {
    test('calculateFishingSpotAlignment should return valid alignment', () {
      final alignment = MarkerAlignmentUtils.calculateFishingSpotAlignment(
        markerSize: 60.0,
        markerWidth: 80.0,
        markerHeight: 96.0,
      );

      // 验证alignment值在有效范围内
      expect(alignment.x, equals(0.0)); // 水平居中
      expect(alignment.y, greaterThanOrEqualTo(-1.0)); // y值在有效范围内
      expect(alignment.y, lessThanOrEqualTo(1.0));
      
      // 由于尖点在图标底部，alignment.y应该是正值（向下偏移）
      expect(alignment.y, greaterThan(0.0));
    });

    test('calculateFishingSpotAlignment should be consistent', () {
      // 多次调用应该返回相同结果
      final alignment1 = MarkerAlignmentUtils.calculateFishingSpotAlignment();
      final alignment2 = MarkerAlignmentUtils.calculateFishingSpotAlignment();
      
      expect(alignment1.x, equals(alignment2.x));
      expect(alignment1.y, equals(alignment2.y));
    });

    test('getRecommendedMarkerSize should return appropriate size', () {
      const iconSize = 60.0;
      final size = MarkerAlignmentUtils.getRecommendedMarkerSize(iconSize);
      
      // 推荐尺寸应该大于图标尺寸
      expect(size.width, greaterThan(iconSize));
      expect(size.height, greaterThan(iconSize));
      
      // 高度应该比宽度大（为尖点留空间）
      expect(size.height, greaterThan(size.width));
    });

    test('calculatePinOffset should return correct offset', () {
      const screenHeight = 800.0;
      const targetHeightPercent = 0.25;
      const pinSize = 40.0;
      
      final offset = MarkerAlignmentUtils.calculatePinOffset(
        screenHeight: screenHeight,
        targetHeightPercent: targetHeightPercent,
        pinSize: pinSize,
      );
      
      // 偏移应该让图钉针尖对准目标位置
      final targetPosition = screenHeight * targetHeightPercent; // 200
      final expectedTipPosition = offset + pinSize * 0.85; // 针尖位置
      
      expect(expectedTipPosition, closeTo(targetPosition, 1.0));
    });

    test('createAlignedFishingSpotMarker should return complete config', () {
      const iconSize = 60.0;
      final config = MarkerAlignmentUtils.createAlignedFishingSpotMarker(
        iconSize: iconSize,
      );
      
      // 验证返回的配置包含所有必要字段
      expect(config, containsPair('width', isA<double>()));
      expect(config, containsPair('height', isA<double>()));
      expect(config, containsPair('alignment', isA<Alignment>()));
      expect(config, containsPair('iconSize', iconSize));
      
      // 验证尺寸合理性
      final width = config['width'] as double;
      final height = config['height'] as double;
      
      expect(width, greaterThan(iconSize));
      expect(height, greaterThan(iconSize));
      expect(height, greaterThan(width));
    });

    test('validateAlignment should not throw for valid parameters', () {
      // 验证方法应该正常运行，不抛出异常
      expect(() {
        MarkerAlignmentUtils.validateAlignment(
          markerSize: 60.0,
          markerWidth: 80.0,
          markerHeight: 96.0,
        );
      }, returnsNormally);
    });

    test('alignment calculation should handle different sizes', () {
      // 测试不同尺寸下的对齐计算
      final sizes = [30.0, 60.0, 90.0, 120.0];
      
      for (final size in sizes) {
        final alignment = MarkerAlignmentUtils.calculateFishingSpotAlignment(
          markerSize: size,
          markerWidth: size * 1.33,
          markerHeight: size * 1.6,
        );
        
        // 所有尺寸下都应该返回有效的对齐值
        expect(alignment.x, equals(0.0));
        expect(alignment.y, greaterThanOrEqualTo(-1.0));
        expect(alignment.y, lessThanOrEqualTo(1.0));
        expect(alignment.y, greaterThan(0.0)); // 尖点在下方
      }
    });

    test('pin offset calculation should handle different screen sizes', () {
      final screenSizes = [600.0, 800.0, 1000.0, 1200.0];
      const targetPercent = 0.25;
      const pinSize = 40.0;
      
      for (final screenHeight in screenSizes) {
        final offset = MarkerAlignmentUtils.calculatePinOffset(
          screenHeight: screenHeight,
          targetHeightPercent: targetPercent,
          pinSize: pinSize,
        );
        
        // 验证针尖位置准确性
        final targetPosition = screenHeight * targetPercent;
        final tipPosition = offset + pinSize * 0.85;
        
        expect(tipPosition, closeTo(targetPosition, 1.0));
        
        // 偏移应该是合理的正值（图钉在目标位置上方）
        expect(offset, greaterThanOrEqualTo(0.0));
        expect(offset, lessThan(screenHeight));
      }
    });

    test('alignment should be proportional to marker dimensions', () {
      // 测试对齐值与标记尺寸的比例关系
      const baseSize = 60.0;
      
      // 保持比例不变，改变绝对尺寸
      final alignment1 = MarkerAlignmentUtils.calculateFishingSpotAlignment(
        markerSize: baseSize,
        markerWidth: baseSize * 1.33,
        markerHeight: baseSize * 1.6,
      );
      
      final alignment2 = MarkerAlignmentUtils.calculateFishingSpotAlignment(
        markerSize: baseSize * 2,
        markerWidth: baseSize * 2 * 1.33,
        markerHeight: baseSize * 2 * 1.6,
      );
      
      // 比例相同时，对齐值应该相同
      expect(alignment1.x, equals(alignment2.x));
      expect(alignment1.y, closeTo(alignment2.y, 0.01));
    });
  });
}
