import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import 'encrypted_r2_service.dart';
import '../config/r2_config.dart' hide ImageUploadResult;
import '../models/upload_result.dart';
import 'service_locator.dart';

/// 安全的图片上传服务
/// 使用加密的 R2 凭据和 Flutter 端签名生成
class SecureImageUploadService {
  final EncryptedR2Service _r2Service = EncryptedR2Service();

  /// 上传单张图片（独立上传版本，不需要钓点ID）
  Future<ImageUploadResult?> uploadImageIndependent({
    required File imageFile,
    required String userId,
  }) async {
    try {
      debugPrint('🔐 [独立上传] 开始独立图片上传');
      debugPrint('🔐 [独立上传] 用户ID: $userId');

      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        debugPrint('❌ [独立上传] 图片验证失败');
        return null;
      }

      // 2. 读取和处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('❌ [独立上传] 无法解码图片');
        return null;
      }

      // 3. 压缩原图
      final compressedImage = _compressImage(originalImage);
      final compressedBytes = img.encodeJpg(
        compressedImage,
        quality: R2Config.imageQuality,
      );

      // 4. 生成缩略图
      final thumbnail = _generateThumbnail(originalImage);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);

      debugPrint('🔐 [独立上传] 图片处理完成');
      debugPrint('🔐 [独立上传] 压缩后大小: ${compressedBytes.length} bytes');
      debugPrint('🔐 [独立上传] 缩略图大小: ${thumbnailBytes.length} bytes');

      // 5. 生成文件路径（使用统一的fishing_app路径）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = timestamp.toString() + '_' + _generateRandomString(8);
      final fileName = path.basename(imageFile.path);
      final extension = path.extension(fileName).toLowerCase().replaceAll('.', '');

      final originalPath = 'fishing_app/$userId/${timestamp}_${uuid}.${extension.isEmpty ? 'jpg' : extension}';
      final thumbnailPath = 'fishing_app/$userId/${timestamp}_${uuid}_thumb.${extension.isEmpty ? 'jpg' : extension}';

      debugPrint('🔐 [独立上传] 原图路径: $originalPath');
      debugPrint('🔐 [独立上传] 缩略图路径: $thumbnailPath');

      // 6. 生成预签名URL
      debugPrint('🔐 [独立上传] 开始生成预签名URL...');

      final originalPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: originalPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      final thumbnailPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: thumbnailPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (originalPresignedUrl == null || thumbnailPresignedUrl == null) {
        debugPrint('❌ [独立上传] 生成预签名URL失败');
        return null;
      }

      debugPrint('✅ [独立上传] 预签名URL生成成功');

      // 7. 上传原图
      debugPrint('🔐 [独立上传] 开始上传原图...');
      final originalUploadSuccess = await _uploadToR2(
        presignedUrl: originalPresignedUrl,
        fileBytes: Uint8List.fromList(compressedBytes),
        contentType: 'image/${extension.isEmpty ? 'jpeg' : extension}',
      );

      if (!originalUploadSuccess) {
        debugPrint('❌ [独立上传] 原图上传失败');
        return null;
      }

      debugPrint('✅ [独立上传] 原图上传成功');

      // 8. 上传缩略图
      debugPrint('🔐 [独立上传] 开始上传缩略图...');
      final thumbnailUploadSuccess = await _uploadToR2(
        presignedUrl: thumbnailPresignedUrl,
        fileBytes: Uint8List.fromList(thumbnailBytes),
        contentType: 'image/${extension.isEmpty ? 'jpeg' : extension}',
      );

      if (!thumbnailUploadSuccess) {
        debugPrint('⚠️ [独立上传] 缩略图上传失败，但原图已成功');
      } else {
        debugPrint('✅ [独立上传] 缩略图上传成功');
      }

      // 9. 获取 R2 凭据以构建公开URL
      final credentials = await _r2Service.getR2Credentials();
      if (credentials == null) {
        debugPrint('❌ [独立上传] 无法获取R2凭据构建公开URL');
        return null;
      }

      // 10. 构建公开访问URL
      final originalUrl = '${credentials.endpoint}/${credentials.bucketName}/$originalPath';
      final thumbnailUrl = thumbnailUploadSuccess
          ? '${credentials.endpoint}/${credentials.bucketName}/$thumbnailPath'
          : originalUrl;

      debugPrint('✅ [独立上传] 上传完成');
      debugPrint('🔐 [独立上传] 原图URL: $originalUrl');
      debugPrint('🔐 [独立上传] 缩略图URL: $thumbnailUrl');

      return ImageUploadResult(
        originalUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: path.basename(imageFile.path),
        fileSize: compressedBytes.length,
        width: compressedImage.width,
        height: compressedImage.height,
      );
    } catch (e) {
      debugPrint('❌ [独立上传] 上传异常: $e');
      return null;
    }
  }

  /// 上传单张图片（安全版本）
  Future<ImageUploadResult?> uploadImageSecure({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    try {
      debugPrint('🔐 [安全上传] 开始安全图片上传');
      debugPrint('🔐 [安全上传] 用户ID: $userId');
      debugPrint('🔐 [安全上传] 钓点ID: $spotId');
      
      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        debugPrint('❌ [安全上传] 图片验证失败');
        return null;
      }

      // 2. 读取和处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('❌ [安全上传] 无法解码图片');
        return null;
      }

      debugPrint('🔐 [安全上传] 图片解码成功: ${originalImage.width}x${originalImage.height}');

      // 3. 压缩原图
      final compressedImage = _compressImage(originalImage);
      final compressedBytes = img.encodeJpg(
        compressedImage,
        quality: R2Config.imageQuality,
      );

      // 4. 生成缩略图
      final thumbnail = _generateThumbnail(originalImage);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);

      debugPrint('🔐 [安全上传] 图片处理完成');
      debugPrint('🔐 [安全上传] 压缩后大小: ${compressedBytes.length} bytes');
      debugPrint('🔐 [安全上传] 缩略图大小: ${thumbnailBytes.length} bytes');

      // 5. 生成文件路径（使用统一的fishing_app路径）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = timestamp.toString() + '_' + _generateRandomString(8);
      final fileName = path.basename(imageFile.path);
      final extension = path.extension(fileName).toLowerCase().replaceAll('.', '');

      final originalPath = 'fishing_app/$userId/${timestamp}_${uuid}.${extension.isEmpty ? 'jpg' : extension}';
      final thumbnailPath = 'fishing_app/$userId/${timestamp}_${uuid}_thumb.${extension.isEmpty ? 'jpg' : extension}';

      debugPrint('🔐 [安全上传] 原图路径: $originalPath');
      debugPrint('🔐 [安全上传] 缩略图路径: $thumbnailPath');

      // 6. 生成预签名URL
      debugPrint('🔐 [安全上传] 开始生成预签名URL...');
      
      final originalPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: originalPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      final thumbnailPresignedUrl = await _r2Service.generatePresignedUrl(
        objectKey: thumbnailPath,
        method: 'PUT',
        expiresIn: 3600,
      );

      if (originalPresignedUrl == null || thumbnailPresignedUrl == null) {
        debugPrint('❌ [安全上传] 生成预签名URL失败');
        return null;
      }

      debugPrint('✅ [安全上传] 预签名URL生成成功');

      // 7. 上传原图
      debugPrint('🔐 [安全上传] 开始上传原图...');
      final originalUploadSuccess = await _uploadToR2(
        presignedUrl: originalPresignedUrl,
        fileBytes: Uint8List.fromList(compressedBytes),
        contentType: 'image/${extension.isEmpty ? 'jpeg' : extension}',
      );

      if (!originalUploadSuccess) {
        debugPrint('❌ [安全上传] 原图上传失败');
        return null;
      }

      debugPrint('✅ [安全上传] 原图上传成功');

      // 8. 上传缩略图
      debugPrint('🔐 [安全上传] 开始上传缩略图...');
      final thumbnailUploadSuccess = await _uploadToR2(
        presignedUrl: thumbnailPresignedUrl,
        fileBytes: Uint8List.fromList(thumbnailBytes),
        contentType: 'image/${extension.isEmpty ? 'jpeg' : extension}',
      );

      if (!thumbnailUploadSuccess) {
        debugPrint('⚠️ [安全上传] 缩略图上传失败，但原图已成功');
        // 缩略图失败不影响主要功能
      } else {
        debugPrint('✅ [安全上传] 缩略图上传成功');
      }

      // 9. 获取 R2 凭据以构建公开URL
      final credentials = await _r2Service.getR2Credentials();
      if (credentials == null) {
        debugPrint('❌ [安全上传] 无法获取R2凭据构建公开URL');
        return null;
      }

      // 10. 构建公开访问URL
      final originalUrl = '${credentials.endpoint}/${credentials.bucketName}/$originalPath';
      final thumbnailUrl = thumbnailUploadSuccess 
          ? '${credentials.endpoint}/${credentials.bucketName}/$thumbnailPath'
          : originalUrl;

      debugPrint('✅ [安全上传] 上传完成');
      debugPrint('🔐 [安全上传] 原图URL: $originalUrl');
      debugPrint('🔐 [安全上传] 缩略图URL: $thumbnailUrl');

      // 11. 返回结果
      return ImageUploadResult(
        originalUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: path.basename(originalPath),
        fileSize: compressedBytes.length,
        width: compressedImage.width,
        height: compressedImage.height,
      );

    } catch (e) {
      debugPrint('❌ [安全上传] 上传异常: $e');
      return null;
    }
  }

  /// 上传到 R2
  Future<bool> _uploadToR2({
    required String presignedUrl,
    required Uint8List fileBytes,
    required String contentType,
  }) async {
    try {
      debugPrint('🔐 [R2上传] 开始上传文件');
      debugPrint('🔐 [R2上传] 文件大小: ${fileBytes.length} bytes');
      debugPrint('🔐 [R2上传] Content-Type: $contentType');
      debugPrint('🔐 [R2上传] URL: ${presignedUrl.substring(0, 100)}...');

      final response = await http.put(
        Uri.parse(presignedUrl),
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      debugPrint('🔐 [R2上传] 响应状态码: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        debugPrint('✅ [R2上传] 上传成功');
        return true;
      } else {
        debugPrint('❌ [R2上传] 上传失败: ${response.statusCode}');
        debugPrint('❌ [R2上传] 响应体: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [R2上传] 上传异常: $e');
      return false;
    }
  }

  /// 验证图片文件
  Future<bool> _validateImage(File imageFile) async {
    try {
      // Web平台的特殊处理
      if (kIsWeb) {
        final extension = path
            .extension(imageFile.path)
            .toLowerCase()
            .replaceAll('.', '');
        if (!R2Config.allowedExtensions.contains(extension)) {
          debugPrint('❌ [验证] 不支持的图片格式: $extension');
          return false;
        }
        return true;
      }

      // 移动平台的完整验证
      if (!await imageFile.exists()) {
        debugPrint('❌ [验证] 文件不存在');
        return false;
      }

      final fileSize = await imageFile.length();
      if (fileSize > R2Config.maxImageSize) {
        debugPrint('❌ [验证] 图片文件过大: ${fileSize}bytes');
        return false;
      }

      final extension = path
          .extension(imageFile.path)
          .toLowerCase()
          .replaceAll('.', '');
      if (!R2Config.allowedExtensions.contains(extension)) {
        debugPrint('❌ [验证] 不支持的图片格式: $extension');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ [验证] 图片验证异常: $e');
      return false;
    }
  }

  /// 压缩图片
  img.Image _compressImage(img.Image image) {
    int width = image.width;
    int height = image.height;

    // 计算缩放比例
    if (width > R2Config.maxImageWidth || height > R2Config.maxImageHeight) {
      final widthRatio = R2Config.maxImageWidth / width;
      final heightRatio = R2Config.maxImageHeight / height;
      final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

      width = (width * ratio).round();
      height = (height * ratio).round();
    }

    return img.copyResize(image, width: width, height: height);
  }

  /// 生成缩略图
  img.Image _generateThumbnail(img.Image image) {
    const thumbnailSize = 300;

    // 计算缩略图尺寸（保持宽高比）
    int width, height;
    if (image.width > image.height) {
      width = thumbnailSize;
      height = (image.height * thumbnailSize / image.width).round();
    } else {
      height = thumbnailSize;
      width = (image.width * thumbnailSize / image.height).round();
    }

    return img.copyResize(image, width: width, height: height);
  }

  /// 生成随机字符串
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    String result = '';
    
    for (int i = 0; i < length; i++) {
      result += chars[(random + i) % chars.length];
    }
    
    return result;
  }
}