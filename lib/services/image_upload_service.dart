import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import '../config/r2_config.dart' hide ImageUploadResult;
import 'presigned_upload_service.dart';
import 'r2_direct_upload_service.dart';
import 'secure_image_upload_service.dart';
import '../widgets/upload_progress_dialog.dart';
import '../models/upload_result.dart';

/// 图片上传服务
///
/// 功能：
/// 1. 图片压缩和优化
/// 2. 生成缩略图
/// 3. 通过预签名URL上传到Cloudflare R2
/// 4. 返回图片URL
class ImageUploadService {
  final PresignedUploadService _presignedService = PresignedUploadService();
  final R2DirectUploadService _r2DirectService = R2DirectUploadService();
  final SecureImageUploadService _secureUploadService = SecureImageUploadService();

  /// 上传单张图片（独立上传版本，不需要钓点ID）
  /// [imageFile] 原始图片文件
  /// [userId] 用户ID
  /// 返回上传结果，包含原图和缩略图URL
  Future<ImageUploadResult?> uploadImageIndependent({
    required File imageFile,
    required String userId,
  }) async {
    debugPrint('🔐 [独立上传] 使用独立上传服务');
    return await _secureUploadService.uploadImageIndependent(
      imageFile: imageFile,
      userId: userId,
    );
  }

  /// 上传单张图片（安全版本）
  /// [imageFile] 原始图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果，包含原图和缩略图URL
  Future<ImageUploadResult?> uploadImageSecure({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    debugPrint('🔐 [安全上传] 使用安全上传服务');
    return await _secureUploadService.uploadImageSecure(
      imageFile: imageFile,
      userId: userId,
      spotId: spotId,
    );
  }

  /// 上传单张图片（传统版本，保持向后兼容）
  /// [imageFile] 原始图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果，包含原图和缩略图URL
  Future<ImageUploadResult?> uploadImage({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    try {
      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        debugPrint('图片验证失败');
        return null;
      }

      // 2. 读取和处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('无法解码图片');
        return null;
      }

      // 3. 压缩原图
      final compressedImage = _compressImage(originalImage);
      final compressedBytes = img.encodeJpg(
        compressedImage,
        quality: R2Config.imageQuality,
      );

      // 4. 生成缩略图
      final thumbnail = _generateThumbnail(originalImage);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);

      // 5. 生成文件名
      final fileName = path.basename(imageFile.path);

      // 6. 使用PocketBase预签名URL方式上传
      debugPrint('🔍 [图片上传] 使用PocketBase预签名URL上传');
      debugPrint('🔍 [图片上传] spotId: $spotId');
      debugPrint('🔍 [图片上传] fileName: $fileName');

      final presignedResponse = await _presignedService.getPresignedUrl(
        spotId: spotId,
        fileName: fileName,
        fileType: 'image/jpeg',
      );

      if (presignedResponse == null) {
        debugPrint('❌ [图片上传] 获取预签名URL失败');
        return null;
      }

      debugPrint('✅ [图片上传] 获取预签名URL成功');

      // 7. 上传原图到R2
      final uploadSuccess = await _presignedService.uploadWithPresignedUrl(
        presignedUrl: presignedResponse.uploadUrl,
        fileBytes: compressedBytes,
        contentType: 'image/jpeg',
      );

      if (!uploadSuccess) {
        debugPrint('原图上传失败');
        return null;
      }

      // 8. 上传缩略图到R2（如果有缩略图URL）
      bool thumbnailUploadSuccess = true;
      if (presignedResponse.thumbnailUploadUrl != null) {
        thumbnailUploadSuccess = await _presignedService.uploadWithPresignedUrl(
          presignedUrl: presignedResponse.thumbnailUploadUrl!,
          fileBytes: thumbnailBytes,
          contentType: 'image/jpeg',
        );

        if (!thumbnailUploadSuccess) {
          debugPrint('缩略图上传失败');
          // 原图已上传成功，缩略图失败不影响主要功能
        }
      }

      debugPrint('图片上传成功: ${presignedResponse.publicUrl}');

      // 9. 返回结果
      return ImageUploadResult(
        originalUrl: presignedResponse.publicUrl,
        thumbnailUrl:
            presignedResponse.thumbnailPublicUrl ?? presignedResponse.publicUrl,
        fileName: presignedResponse.filePath.split('/').last,
        fileSize: compressedBytes.length,
        width: originalImage.width,
        height: originalImage.height,
      );
    } catch (e) {
      debugPrint('图片上传异常: $e');
      return null;
    }
  }

  /// 使用R2直接上传图片（新方法）
  /// [imageFile] 原始图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果，包含原图和缩略图URL
  Future<ImageUploadResult?> uploadImageDirect({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    try {
      debugPrint('🚀 [直接上传] 开始使用R2直接上传');
      debugPrint('🔍 [直接上传] 文件: ${imageFile.path}');
      debugPrint('🔍 [直接上传] 用户ID: $userId');
      debugPrint('🔍 [直接上传] 钓点ID: $spotId');

      // 使用R2直接上传服务
      final result = await _r2DirectService.uploadImageDirect(
        imageFile: imageFile,
        spotId: spotId,
      );

      if (result != null) {
        debugPrint('✅ [直接上传] 上传成功');
        debugPrint('🔍 [直接上传] 原图URL: ${result.originalUrl}');
        debugPrint('🔍 [直接上传] 缩略图URL: ${result.thumbnailUrl}');
      } else {
        debugPrint('❌ [直接上传] 上传失败');
      }

      return result;
    } catch (e) {
      debugPrint('❌ [直接上传] 上传异常: $e');
      return null;
    }
  }

  /// 批量上传图片 - 并行处理优化版
  Future<List<ImageUploadResult>> uploadImages({
    required List<File> imageFiles,
    required String userId,
    required String spotId,
  }) async {
    if (imageFiles.isEmpty) {
      debugPrint('没有图片需要上传');
      return [];
    }

    debugPrint('开始批量上传 ${imageFiles.length} 张图片');

    // 限制并发数量，避免过多同时请求
    const int maxConcurrent = 3;
    final results = <ImageUploadResult>[];

    // 分批处理图片上传
    for (int i = 0; i < imageFiles.length; i += maxConcurrent) {
      final batch = imageFiles.skip(i).take(maxConcurrent).toList();
      debugPrint('处理第 ${(i ~/ maxConcurrent) + 1} 批，包含 ${batch.length} 张图片');

      // 并行上传当前批次的图片
      final futures = batch.map(
        (imageFile) =>
            uploadImage(imageFile: imageFile, userId: userId, spotId: spotId),
      );

      try {
        final batchResults = await Future.wait(futures);

        // 收集成功上传的结果
        for (final result in batchResults) {
          if (result != null) {
            results.add(result);
          }
        }

        debugPrint(
          '第 ${(i ~/ maxConcurrent) + 1} 批上传完成，成功 ${batchResults.where((r) => r != null).length}/${batch.length} 张',
        );
      } catch (e) {
        debugPrint('第 ${(i ~/ maxConcurrent) + 1} 批上传出现错误: $e');

        // 如果批量上传失败，尝试逐个上传
        for (final imageFile in batch) {
          try {
            final result = await uploadImage(
              imageFile: imageFile,
              userId: userId,
              spotId: spotId,
            );
            if (result != null) {
              results.add(result);
            }
          } catch (individualError) {
            debugPrint('单张图片上传失败: $individualError');
          }
        }
      }

      // 在批次之间添加短暂延迟，避免服务器压力过大
      if (i + maxConcurrent < imageFiles.length) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    debugPrint('批量上传完成，成功上传 ${results.length}/${imageFiles.length} 张图片');
    return results;
  }

  /// 批量上传图片 - 带进度回调版本
  Future<List<ImageUploadResult>> uploadImagesWithProgress({
    required List<File> imageFiles,
    required String userId,
    required String spotId,
    required StreamController<UploadProgress> progressController,
  }) async {
    if (imageFiles.isEmpty) {
      debugPrint('没有图片需要上传');
      progressController.add(
        UploadProgress(
          uploadedCount: 0,
          failedCount: 0,
          currentFileName: '',
          isCompleted: true,
          failedFiles: [],
        ),
      );
      return [];
    }

    debugPrint('开始批量上传 ${imageFiles.length} 张图片（带进度回调）');

    int uploadedCount = 0;
    int failedCount = 0;
    final List<String> failedFiles = [];
    final results = <ImageUploadResult>[];

    // 限制并发数量
    const int maxConcurrent = 3;

    // 分批处理图片上传
    for (int i = 0; i < imageFiles.length; i += maxConcurrent) {
      final batch = imageFiles.skip(i).take(maxConcurrent).toList();

      // 为当前批次创建上传任务
      final futures = batch.map((imageFile) async {
        final fileName = path.basename(imageFile.path);

        // 更新当前上传文件
        progressController.add(
          UploadProgress(
            uploadedCount: uploadedCount,
            failedCount: failedCount,
            currentFileName: fileName,
            isCompleted: false,
            failedFiles: List.from(failedFiles),
          ),
        );

        try {
          final result = await uploadImage(
            imageFile: imageFile,
            userId: userId,
            spotId: spotId,
          );

          if (result != null) {
            uploadedCount++;
            return result;
          } else {
            failedCount++;
            failedFiles.add(fileName);
            return null;
          }
        } catch (e) {
          debugPrint('上传图片失败 $fileName: $e');
          failedCount++;
          failedFiles.add(fileName);
          return null;
        }
      });

      try {
        final batchResults = await Future.wait(futures);

        // 收集成功上传的结果
        for (final result in batchResults) {
          if (result != null) {
            results.add(result);
          }
        }

        // 更新进度
        progressController.add(
          UploadProgress(
            uploadedCount: uploadedCount,
            failedCount: failedCount,
            currentFileName: '',
            isCompleted: false,
            failedFiles: List.from(failedFiles),
          ),
        );
      } catch (e) {
        debugPrint('批次上传出现错误: $e');
      }

      // 在批次之间添加短暂延迟
      if (i + maxConcurrent < imageFiles.length) {
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }

    // 上传完成
    progressController.add(
      UploadProgress(
        uploadedCount: uploadedCount,
        failedCount: failedCount,
        currentFileName: '',
        isCompleted: true,
        failedFiles: List.from(failedFiles),
      ),
    );

    debugPrint('批量上传完成，成功上传 ${results.length}/${imageFiles.length} 张图片');
    return results;
  }

  /// 验证图片文件
  Future<bool> _validateImage(File imageFile) async {
    try {
      // Web平台的特殊处理
      if (kIsWeb) {
        // Web平台无法直接检查文件是否存在，跳过存在性检查
        // 只检查文件扩展名
        final extension = path
            .extension(imageFile.path)
            .toLowerCase()
            .replaceAll('.', '');
        if (!R2Config.allowedExtensions.contains(extension)) {
          debugPrint('不支持的图片格式: $extension');
          return false;
        }
        debugPrint('Web平台图片验证通过: ${imageFile.path}');
        return true;
      }

      // 移动平台的完整验证
      // 检查文件是否存在
      if (!await imageFile.exists()) {
        return false;
      }

      // 检查文件大小
      final fileSize = await imageFile.length();
      if (fileSize > R2Config.maxImageSize) {
        debugPrint('图片文件过大: ${fileSize}bytes');
        return false;
      }

      // 检查文件扩展名
      final extension = path
          .extension(imageFile.path)
          .toLowerCase()
          .replaceAll('.', '');
      if (!R2Config.allowedExtensions.contains(extension)) {
        debugPrint('不支持的图片格式: $extension');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('图片验证异常: $e');
      return false;
    }
  }

  /// 压缩图片
  img.Image _compressImage(img.Image image) {
    int width = image.width;
    int height = image.height;

    // 计算缩放比例
    if (width > R2Config.maxImageWidth || height > R2Config.maxImageHeight) {
      final widthRatio = R2Config.maxImageWidth / width;
      final heightRatio = R2Config.maxImageHeight / height;
      final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

      width = (width * ratio).round();
      height = (height * ratio).round();
    }

    return img.copyResize(image, width: width, height: height);
  }

  /// 生成缩略图
  img.Image _generateThumbnail(img.Image image) {
    const thumbnailSize = 300;

    // 计算缩略图尺寸（保持宽高比）
    int width, height;
    if (image.width > image.height) {
      width = thumbnailSize;
      height = (image.height * thumbnailSize / image.width).round();
    } else {
      height = thumbnailSize;
      width = (image.width * thumbnailSize / image.height).round();
    }

    return img.copyResize(image, width: width, height: height);
  }
}
