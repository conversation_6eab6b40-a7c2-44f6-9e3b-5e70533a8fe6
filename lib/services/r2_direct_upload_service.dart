import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
// import 'package:crypto/crypto.dart'; // 不再需要
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;

import '../config/app_config.dart';
import '../config/pocketbase_config.dart';
import '../models/upload_result.dart';

/// R2直接上传服务
/// 用户直接使用令牌上传图片到Cloudflare R2
class R2DirectUploadService {
  /// 获取R2访问令牌
  Future<R2Token?> getR2Token() async {
    try {
      final pb = PocketBaseConfig.instance.client;
      final token = pb.authStore.token;

      if (token.isEmpty) {
        debugPrint('❌ [R2令牌] 用户未登录');
        return null;
      }

      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final url = '$baseUrl/api/get-r2-token';

      debugPrint('🔍 [R2令牌] 请求URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      debugPrint('🔍 [R2令牌] 响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('✅ [R2令牌] 获取R2令牌成功');
        return R2Token.fromJson(data);
      } else {
        debugPrint(
          '❌ [R2令牌] 获取R2令牌失败: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      debugPrint('❌ [R2令牌] 获取R2令牌异常: $e');
      return null;
    }
  }

  /// 直接上传图片到R2
  Future<ImageUploadResult?> uploadImageDirect({
    required File imageFile,
    required String spotId,
  }) async {
    try {
      // 1. 获取R2令牌
      final r2Token = await getR2Token();
      if (r2Token == null) {
        debugPrint('❌ [R2上传] 获取R2令牌失败');
        return null;
      }

      // 2. 处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('❌ [R2上传] 无法解码图片');
        return null;
      }

      // 3. 压缩原图
      final compressedImage = _compressImage(originalImage);
      final compressedBytes = img.encodeJpg(compressedImage, quality: 85);

      // 4. 生成缩略图
      final thumbnail = _generateThumbnail(originalImage);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);

      // 5. 生成文件路径（使用统一的fishing_app路径）
      final fileName = path.basename(imageFile.path);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = _generateUuid();
      final extension = path.extension(fileName).toLowerCase();

      final originalPath =
          'fishing_app/${r2Token.userId}/${timestamp}_${uuid}_original$extension';
      final thumbnailPath =
          'fishing_app/${r2Token.userId}/${timestamp}_${uuid}_thumb$extension';

      // 6. 上传原图
      final originalUrl = await _uploadToR2(
        r2Token: r2Token,
        filePath: originalPath,
        fileBytes: compressedBytes,
        contentType: 'image/jpeg',
      );

      if (originalUrl == null) {
        debugPrint('❌ [R2上传] 原图上传失败');
        return null;
      }

      // 7. 上传缩略图
      final thumbnailUrl = await _uploadToR2(
        r2Token: r2Token,
        filePath: thumbnailPath,
        fileBytes: thumbnailBytes,
        contentType: 'image/jpeg',
      );

      if (thumbnailUrl == null) {
        debugPrint('❌ [R2上传] 缩略图上传失败');
        return null;
      }

      // 8. 保存图片URL到PocketBase
      final saveSuccess = await _saveImageUrlToPocketBase(
        spotId: spotId,
        imageUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: fileName,
      );

      if (!saveSuccess) {
        debugPrint('❌ [R2上传] 保存图片URL到PocketBase失败');
        return null;
      }

      debugPrint('✅ [R2上传] 图片上传完成');

      return ImageUploadResult(
        originalUrl: originalUrl,
        thumbnailUrl: thumbnailUrl,
        fileName: fileName,
        fileSize: compressedBytes.length,
        width: originalImage.width,
        height: originalImage.height,
      );
    } catch (e) {
      debugPrint('❌ [R2上传] 上传图片异常: $e');
      return null;
    }
  }

  /// 上传文件到R2（简化版本，不使用AWS签名）
  Future<String?> _uploadToR2({
    required R2Token r2Token,
    required String filePath,
    required Uint8List fileBytes,
    required String contentType,
  }) async {
    try {
      final url = '${r2Token.endpoint}/${r2Token.bucketName}/$filePath';

      debugPrint('🔍 [R2上传] 上传URL: $url');
      debugPrint('🔍 [R2上传] 文件大小: ${fileBytes.length} bytes');

      // 简化版本：直接PUT上传，不使用AWS签名
      final response = await http.put(
        Uri.parse(url),
        body: fileBytes,
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileBytes.length.toString(),
        },
      );

      debugPrint('🔍 [R2上传] 响应状态码: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        debugPrint('✅ [R2上传] 文件上传成功');
        return url;
      } else {
        debugPrint(
          '❌ [R2上传] 文件上传失败: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      debugPrint('❌ [R2上传] 上传文件异常: $e');
      return null;
    }
  }

  /// 保存图片URL到PocketBase
  Future<bool> _saveImageUrlToPocketBase({
    required String spotId,
    required String imageUrl,
    required String thumbnailUrl,
    required String fileName,
  }) async {
    try {
      final pb = PocketBaseConfig.instance.client;
      final token = pb.authStore.token;

      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final url = '$baseUrl/api/save-image-url';

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'spotId': spotId,
          'imageUrl': imageUrl,
          'thumbnailUrl': thumbnailUrl,
          'fileName': fileName,
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('✅ [保存URL] 图片URL已保存到PocketBase');
        return true;
      } else {
        debugPrint('❌ [保存URL] 保存失败: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [保存URL] 保存异常: $e');
      return false;
    }
  }

  /// 压缩图片
  img.Image _compressImage(img.Image image) {
    const maxWidth = 1920;
    const maxHeight = 1080;

    if (image.width <= maxWidth && image.height <= maxHeight) {
      return image;
    }

    return img.copyResize(
      image,
      width: image.width > maxWidth ? maxWidth : null,
      height: image.height > maxHeight ? maxHeight : null,
      maintainAspect: true,
    );
  }

  /// 生成缩略图
  img.Image _generateThumbnail(img.Image image) {
    const thumbnailSize = 300;
    return img.copyResize(
      image,
      width: thumbnailSize,
      height: thumbnailSize,
      maintainAspect: true,
    );
  }

  /// 生成简单的UUID
  String _generateUuid() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toRadixString(36);
    return random;
  }
}

/// R2令牌模型
class R2Token {
  final String endpoint;
  final String bucketName;
  final String accessKeyId;
  final String secretAccessKey;
  final String region;
  final String userId;
  final int expiresIn;

  R2Token({
    required this.endpoint,
    required this.bucketName,
    required this.accessKeyId,
    required this.secretAccessKey,
    required this.region,
    required this.userId,
    required this.expiresIn,
  });

  factory R2Token.fromJson(Map<String, dynamic> json) {
    return R2Token(
      endpoint: json['endpoint'],
      bucketName: json['bucketName'],
      accessKeyId: json['accessKeyId'],
      secretAccessKey: json['secretAccessKey'],
      region: json['region'],
      userId: json['userId'],
      expiresIn: json['expiresIn'],
    );
  }
}
