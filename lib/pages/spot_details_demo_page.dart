import 'package:flutter/material.dart';
import '../models/fishing_spot.dart';
import '../models/spot_comment.dart';
import '../widgets/spot_details_sheet.dart';

/// 钓点详情页面演示
/// 
/// 用于展示重新设计的钓点详情UI
class SpotDetailsDemoPage extends StatelessWidget {
  const SpotDetailsDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('钓点详情UI演示'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '钓点详情页面重新设计',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              '新特性：',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 12),
            
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('• 英雄区域 - 大图展示和基本信息'),
                Text('• 照片轮播 - 支持多张照片切换'),
                Text('• 标签页设计 - 详情、照片、评论分类展示'),
                Text('• 现代化UI - 卡片式设计和渐变效果'),
                Text('• 互动功能 - 点赞、收藏、分享'),
                Text('• 照片查看器 - 全屏查看和缩放'),
              ],
            ),
            
            const SizedBox(height: 40),
            
            ElevatedButton(
              onPressed: () => _showDemoSpotDetails(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                '查看演示',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示演示钓点详情
  void _showDemoSpotDetails(BuildContext context) {
    // 创建演示数据
    final demoSpot = FishingSpot(
      id: 'demo_spot_001',
      name: '西湖断桥钓点',
      description: '这是一个风景优美的钓点，位于西湖断桥附近。水质清澈，鱼类丰富，是钓鱼爱好者的理想选择。这里有着悠久的钓鱼历史，许多钓友都在这里收获满满。',
      latitude: 30.2594,
      longitude: 120.1507,
      userId: 'demo_user_001',
      address: '浙江省杭州市西湖区断桥残雪景点附近',
      spotType: 'freshwater',
      fishTypes: 'crucian_carp',
      spotEmoji: '🏞️',
      fishEmoji: '🐟',
      isPublic: true,
      status: 'active',
      created: DateTime.now().subtract(const Duration(days: 3)),
      updated: DateTime.now().subtract(const Duration(days: 1)),
    );

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SpotDetailsSheet(spot: demoSpot),
    );
  }
}
