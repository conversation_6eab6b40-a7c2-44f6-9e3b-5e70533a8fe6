import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../models/fishing_spot.dart';
import '../models/emoji_marker.dart';
import '../models/spot_photo.dart';
import '../config/pocketbase_config.dart';

/// 钓点详情底部弹窗 - 重新设计版本
///
/// 特性：
/// - 现代化的UI设计
/// - 重点展示照片缩略图
/// - 分类展示钓点信息
/// - 良好的用户体验
class SpotDetailsSheet extends StatefulWidget {
  final FishingSpot spot;

  const SpotDetailsSheet({
    super.key,
    required this.spot,
  });

  @override
  State<SpotDetailsSheet> createState() => _SpotDetailsSheetState();
}

class _SpotDetailsSheetState extends State<SpotDetailsSheet> with TickerProviderStateMixin {
  List<SpotPhoto> _photos = [];
  bool _isLoadingPhotos = true;
  int _currentPhotoIndex = 0;
  late TabController _tabController;
  final CarouselSliderController _carouselController = CarouselSliderController();

  // 页面状态
  bool _isLiked = false;
  bool _isDisliked = false;
  bool _isFavorited = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSpotPhotos();
    _loadUserInteractions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载钓点照片
  Future<void> _loadSpotPhotos() async {
    try {
      // 从PocketBase直接获取钓点照片
      final pb = PocketBaseConfig.instance.client;
      final records = await pb
          .collection('spot_photos')
          .getFullList(
            filter: 'spot_id = "${widget.spot.id}"',
            sort: 'sort_order,created',
          );

      final photos = records.map((record) => SpotPhoto.fromJson(record.toJson())).toList();

      // 检查组件是否仍然mounted
      if (mounted) {
        setState(() {
          _photos = photos;
          _isLoadingPhotos = false;
        });
      }
    } catch (e) {
      debugPrint('加载钓点照片失败: $e');
      // 检查组件是否仍然mounted
      if (mounted) {
        setState(() {
          _isLoadingPhotos = false;
        });
      }
    }
  }

  /// 加载用户互动状态
  Future<void> _loadUserInteractions() async {
    try {
      // TODO: 从服务获取用户的点赞、收藏状态
      if (mounted) {
        setState(() {
          _isLiked = false;
          _isDisliked = false;
          _isFavorited = false;
        });
      }
    } catch (e) {
      debugPrint('加载用户互动状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            width: 48,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),

          // 英雄区域 - 照片轮播和基本信息
          _buildHeroSection(),

          // 标签页内容
          Expanded(
            child: Column(
              children: [
                // 标签页导航
                _buildTabBar(),

                // 标签页内容
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 详细信息页
                      _buildDetailsTab(),
                      // 照片页
                      _buildPhotosTab(),
                      // 评论页
                      _buildCommentsTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 底部操作栏
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  /// 构建英雄区域 - 照片轮播和基本信息
  Widget _buildHeroSection() {
    return Container(
      height: 280,
      child: Stack(
        children: [
          // 照片轮播
          _buildPhotoCarousel(),

          // 渐变遮罩
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
          ),

          // 钓点基本信息
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 钓点名称
                Text(
                  widget.spot.name,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // 基本信息行
                Row(
                  children: [
                    // 发布者
                    const Icon(
                      Icons.person_outline,
                      size: 16,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.spot.sharedBy,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // 发布时间
                    const Icon(
                      Icons.access_time,
                      size: 16,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(widget.spot.created),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),

                    const Spacer(),

                    // 照片数量指示器
                    if (_photos.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_currentPhotoIndex + 1}/${_photos.length}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建照片轮播
  Widget _buildPhotoCarousel() {
    if (_isLoadingPhotos) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_photos.isEmpty) {
      return Container(
        color: Colors.grey.shade100,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.photo_camera_outlined,
                size: 48,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 12),
              Text(
                '暂无照片',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return CarouselSlider.builder(
      carouselController: _carouselController,
      itemCount: _photos.length,
      itemBuilder: (context, index, realIndex) {
        final photo = _photos[index];
        final imageUrl = photo.url;

        return GestureDetector(
          onTap: () => _showPhotoViewer(index),
          child: Container(
            width: double.infinity,
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey.shade200,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.shade200,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.broken_image,
                          size: 48,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '图片加载失败',
                          style: TextStyle(
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
      options: CarouselOptions(
        height: 280,
        viewportFraction: 1.0,
        enableInfiniteScroll: _photos.length > 1,
        autoPlay: false,
        onPageChanged: (index, reason) {
          setState(() {
            _currentPhotoIndex = index;
          });
        },
      ),
    );
  }

  /// 构建标签页导航栏
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.blue.shade600,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: Colors.blue.shade600,
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.info_outline, size: 18),
                const SizedBox(width: 6),
                const Text('详情'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.photo_library_outlined, size: 18),
                const SizedBox(width: 6),
                Text('照片${_photos.isNotEmpty ? '(${_photos.length})' : ''}'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.chat_bubble_outline, size: 18),
                const SizedBox(width: 6),
                Text('评论${widget.spot.comments.isNotEmpty ? '(${widget.spot.comments.length})' : ''}'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详情标签页
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 钓点详细信息
          _buildDetailsSection(),

          const SizedBox(height: 20),

          // 描述信息
          _buildDescriptionSection(),

          const SizedBox(height: 20),

          // 位置信息
          _buildLocationSection(),
        ],
      ),
    );
  }

  /// 构建照片标签页
  Widget _buildPhotosTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isLoadingPhotos)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(40),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_photos.isEmpty)
            _buildNoPhotosPlaceholder()
          else
            _buildPhotoGrid(),
        ],
      ),
    );
  }

  /// 构建评论标签页
  Widget _buildCommentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论列表或占位符
          widget.spot.comments.isEmpty
              ? _buildNoCommentsPlaceholder()
              : _buildCommentsList(),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 点赞按钮
          _buildActionButton(
            icon: _isLiked ? FontAwesomeIcons.solidThumbsUp : FontAwesomeIcons.thumbsUp,
            label: '点赞',
            count: widget.spot.likes,
            color: _isLiked ? Colors.blue : Colors.grey.shade600,
            onTap: _handleLike,
          ),

          const SizedBox(width: 16),

          // 评论按钮
          _buildActionButton(
            icon: FontAwesomeIcons.comment,
            label: '评论',
            count: widget.spot.comments.length,
            color: Colors.grey.shade600,
            onTap: _handleComment,
          ),

          const SizedBox(width: 16),

          // 收藏按钮
          _buildActionButton(
            icon: _isFavorited ? FontAwesomeIcons.solidBookmark : FontAwesomeIcons.bookmark,
            label: '收藏',
            count: null,
            color: _isFavorited ? Colors.orange : Colors.grey.shade600,
            onTap: _handleFavorite,
          ),

          const Spacer(),

          // 分享按钮
          ElevatedButton.icon(
            onPressed: _handleShare,
            icon: const FaIcon(FontAwesomeIcons.share, size: 16),
            label: const Text('分享'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(
            icon,
            size: 20,
            color: color,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (count != null && count > 0) ...[
            const SizedBox(height: 2),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 11,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建位置信息区域
  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 位置信息标题
        const Row(
          children: [
            Icon(
              Icons.location_on,
              size: 20,
              color: Colors.red,
            ),
            SizedBox(width: 8),
            Text(
              '位置信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 位置信息卡片
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
          ),
          child: Column(
            children: [
              // 坐标信息
              _buildInfoItem(
                icon: Icons.gps_fixed,
                label: '坐标位置',
                value: '${widget.spot.latitude.toStringAsFixed(6)}, ${widget.spot.longitude.toStringAsFixed(6)}',
                emoji: '📍',
              ),

              // 地址信息（如果有）
              if (widget.spot.address != null && widget.spot.address!.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildInfoItem(
                  icon: Icons.place,
                  label: '详细地址',
                  value: widget.spot.address!,
                  emoji: '🏠',
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建标题区域
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 钓点名称
        Text(
          widget.spot.name,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 基本信息行
        Row(
          children: [
            // 发布者
            const Icon(
              Icons.person_outline,
              size: 16,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            Text(
              widget.spot.sharedBy,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 发布时间
            const Icon(
              Icons.access_time,
              size: 16,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            Text(
              _formatDate(widget.spot.created),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建照片展示区域
  Widget _buildPhotoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 照片标题
        Row(
          children: [
            const Icon(
              Icons.photo_library,
              size: 20,
              color: Colors.blue,
            ),
            const SizedBox(width: 8),
            const Text(
              '钓点照片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            if (_photos.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_photos.length}张',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // 照片网格或占位符
        _isLoadingPhotos
            ? _buildPhotoLoadingPlaceholder()
            : _photos.isEmpty
                ? _buildNoPhotosPlaceholder()
                : _buildPhotoGrid(),
      ],
    );
  }

  /// 构建照片加载占位符
  Widget _buildPhotoLoadingPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// 构建无照片占位符
  Widget _buildNoPhotosPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 32,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无照片',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建照片网格
  Widget _buildPhotoGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _photos.length > 6 ? 6 : _photos.length,
      itemBuilder: (context, index) {
        if (index == 5 && _photos.length > 6) {
          // 显示"更多"按钮
          return _buildMorePhotosButton(_photos.length - 5);
        }

        return _buildPhotoThumbnail(_photos[index], index);
      },
    );
  }

  /// 构建照片缩略图
  Widget _buildPhotoThumbnail(SpotPhoto photo, int index) {
    final imageUrl = photo.thumbnailUrl ?? photo.url;

    return GestureDetector(
      onTap: () => _showPhotoViewer(index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 照片
              Image.network(
                imageUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey.shade200,
                    child: const Center(
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.broken_image,
                      color: Colors.grey.shade400,
                    ),
                  );
                },
              ),

              // 全景照片标识
              if (photo.type == 'panorama')
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '360°',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建更多照片按钮
  Widget _buildMorePhotosButton(int remainingCount) {
    return GestureDetector(
      onTap: () => _showAllPhotos(),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.more_horiz,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                '+$remainingCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  /// 显示照片查看器
  void _showPhotoViewer(int initialIndex) {
    if (_photos.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog.fullscreen(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            // 照片轮播
            Center(
              child: CarouselSlider.builder(
                itemCount: _photos.length,
                itemBuilder: (context, index, realIndex) {
                  final photo = _photos[index];
                  return InteractiveViewer(
                    child: Image.network(
                      photo.url,
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                size: 64,
                                color: Colors.white54,
                              ),
                              SizedBox(height: 16),
                              Text(
                                '图片加载失败',
                                style: TextStyle(
                                  color: Colors.white54,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                },
                options: CarouselOptions(
                  height: double.infinity,
                  viewportFraction: 1.0,
                  enableInfiniteScroll: _photos.length > 1,
                  initialPage: initialIndex,
                ),
              ),
            ),

            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示所有照片
  void _showAllPhotos() {
    // TODO: 实现显示所有照片的页面
    debugPrint('显示所有照片');
  }

  /// 构建钓点详细信息区域
  Widget _buildDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 详细信息标题
        const Row(
          children: [
            Icon(
              Icons.info_outline,
              size: 20,
              color: Colors.green,
            ),
            SizedBox(width: 8),
            Text(
              '钓点信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 信息卡片
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.withValues(alpha: 0.2)),
          ),
          child: Column(
            children: [
              // 钓点类型和鱼类类型
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.location_on,
                      label: '钓点类型',
                      value: _getSpotTypeName(widget.spot.spotType),
                      emoji: widget.spot.spotEmoji,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.pets,
                      label: '鱼类类型',
                      value: _getFishTypeName(widget.spot.fishTypes),
                      emoji: widget.spot.fishEmoji,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 坐标信息
              _buildInfoItem(
                icon: Icons.gps_fixed,
                label: '坐标位置',
                value: '${widget.spot.latitude.toStringAsFixed(6)}, ${widget.spot.longitude.toStringAsFixed(6)}',
                emoji: '📍',
              ),

              // 地址信息（如果有）
              if (widget.spot.address != null && widget.spot.address!.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildInfoItem(
                  icon: Icons.place,
                  label: '详细地址',
                  value: widget.spot.address!,
                  emoji: '🏠',
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建信息项
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    String? emoji,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // emoji或图标
        if (emoji != null)
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          )
        else
          Icon(
            icon,
            size: 16,
            color: Colors.green.shade600,
          ),

        const SizedBox(width: 8),

        // 标签和值
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建描述区域
  Widget _buildDescriptionSection() {
    if (widget.spot.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 描述标题
        const Row(
          children: [
            Icon(
              Icons.description_outlined,
              size: 20,
              color: Colors.orange,
            ),
            SizedBox(width: 8),
            Text(
              '详细描述',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 描述内容
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
          ),
          child: Text(
            widget.spot.description,
            style: const TextStyle(
              fontSize: 15,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建互动区域
  Widget _buildInteractionSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 点赞
          _buildInteractionButton(
            icon: FontAwesomeIcons.thumbsUp,
            label: '点赞',
            count: widget.spot.likes,
            color: Colors.blue,
            onTap: () => _handleLike(),
          ),

          // 不喜欢
          _buildInteractionButton(
            icon: FontAwesomeIcons.thumbsDown,
            label: '不喜欢',
            count: widget.spot.unlikes,
            color: Colors.red,
            onTap: () => _handleDislike(),
          ),

          // 评论
          _buildInteractionButton(
            icon: FontAwesomeIcons.comment,
            label: '评论',
            count: widget.spot.comments.length,
            color: Colors.green,
            onTap: () => _handleComment(),
          ),

          // 分享
          _buildInteractionButton(
            icon: FontAwesomeIcons.share,
            label: '分享',
            count: null,
            color: Colors.purple,
            onTap: () => _handleShare(),
          ),
        ],
      ),
    );
  }

  /// 构建互动按钮
  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: FaIcon(
              icon,
              size: 18,
              color: color,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (count != null) ...[
            const SizedBox(height: 2),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建评论区域
  Widget _buildCommentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 评论标题
        Row(
          children: [
            const Icon(
              Icons.chat_bubble_outline,
              size: 20,
              color: Colors.purple,
            ),
            const SizedBox(width: 8),
            const Text(
              '评论',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            if (widget.spot.comments.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${widget.spot.comments.length}条',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.purple.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // 评论列表或占位符
        widget.spot.comments.isEmpty
            ? _buildNoCommentsPlaceholder()
            : _buildCommentsList(),
      ],
    );
  }

  /// 构建无评论占位符
  Widget _buildNoCommentsPlaceholder() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 32,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            '暂无评论',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '成为第一个评论的人吧',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade400,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建评论列表
  Widget _buildCommentsList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.2)),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        itemCount: widget.spot.comments.length > 3 ? 3 : widget.spot.comments.length,
        separatorBuilder: (context, index) => const Divider(height: 16),
        itemBuilder: (context, index) {
          final comment = widget.spot.comments[index];
          return _buildCommentItem(comment);
        },
      ),
    );
  }

  /// 构建评论项
  Widget _buildCommentItem(dynamic comment) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 用户头像
        CircleAvatar(
          radius: 16,
          backgroundColor: Colors.purple.withValues(alpha: 0.2),
          child: Text(
            comment.username.isNotEmpty ? comment.username[0].toUpperCase() : '?',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.purple.shade700,
            ),
          ),
        ),

        const SizedBox(width: 12),

        // 评论内容
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名和时间
              Row(
                children: [
                  Text(
                    comment.username,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(comment.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              // 评论内容
              Text(
                comment.content,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 获取钓点类型名称
  String _getSpotTypeName(String? spotType) {
    if (spotType == null) return '未知';

    final marker = FishingSpotMarkers.spotTypes.firstWhere(
      (m) => m.type == spotType,
      orElse: () => FishingSpotMarkers.spotTypes.first,
    );
    return marker.name;
  }

  /// 获取鱼类类型名称
  String _getFishTypeName(String? fishType) {
    if (fishType == null) return '未知';

    final marker = FishingSpotMarkers.fishTypes.firstWhere(
      (m) => m.type == fishType,
      orElse: () => FishingSpotMarkers.fishTypes.first,
    );
    return marker.name;
  }

  /// 处理点赞
  void _handleLike() {
    // TODO: 实现点赞功能
    debugPrint('点赞钓点: ${widget.spot.id}');
  }

  /// 处理不喜欢
  void _handleDislike() {
    // TODO: 实现不喜欢功能
    debugPrint('不喜欢钓点: ${widget.spot.id}');
  }

  /// 处理评论
  void _handleComment() {
    // TODO: 实现评论功能
    debugPrint('评论钓点: ${widget.spot.id}');
  }

  /// 处理收藏
  void _handleFavorite() {
    setState(() {
      _isFavorited = !_isFavorited;
    });

    if (_isFavorited) {
      debugPrint('收藏钓点: ${widget.spot.id}');
      // TODO: 调用收藏服务
    } else {
      debugPrint('取消收藏钓点: ${widget.spot.id}');
      // TODO: 调用取消收藏服务
    }
  }

  /// 处理分享
  void _handleShare() {
    // TODO: 实现分享功能
    debugPrint('分享钓点: ${widget.spot.id}');
  }
}
