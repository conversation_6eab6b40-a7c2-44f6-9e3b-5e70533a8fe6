import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/fishing_spot.dart';

/// 钓点标记组件
///
/// 特性：
/// - 圆形背景，底部有尖点指向钓点位置
/// - 背景颜色根据点赞数量从白色渐变到红色
/// - 显示钓点的emoji图标
/// - 支持点击交互
class FishingSpotMarker extends StatelessWidget {
  final FishingSpot spot;
  final int likesCount;
  final VoidCallback? onTap;
  final double size;

  const FishingSpotMarker({
    super.key,
    required this.spot,
    required this.likesCount,
    this.onTap,
    this.size = 60.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: size,
        height: size, // 改为正方形，尖点在中心
        child: CustomPaint(
          painter: _FishingSpotMarkerPainter(
            likesCount: likesCount,
            size: size,
            spot: spot,
          ),
          size: Size(size, size),
        ),
      ),
    );
  }
}

/// 钓点标记绘制器
class _FishingSpotMarkerPainter extends CustomPainter {
  final int likesCount;
  final double size;
  final FishingSpot spot;

  _FishingSpotMarkerPainter({
    required this.likesCount,
    required this.size,
    required this.spot,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    final center = Offset(canvasSize.width / 2, canvasSize.height*1.25);
    final circleRadius = size * 0.35;
    final triangleHeight = size * 1;

    // 重新设计：让尖端位于canvas中心
    // 圆心位置 = 中心点向上偏移三角形高度
    final circleCenter = Offset(center.dx, center.dy-triangleHeight-circleRadius);

    // 获取基础颜色
    final baseColor = _getBackgroundColor(likesCount);

    // 绘制完整的标记形状（圆形+尖点）
    _drawMarkerShape(canvas, circleCenter, circleRadius, baseColor);

    // 绘制轻微的立体效果
    _drawSubtle3DEffect(canvas, circleCenter, circleRadius, baseColor);

    // 绘制简洁边框
    _drawCleanBorder(canvas, circleCenter, circleRadius);

    // 绘制emoji在圆心位置
    _drawEmoji(canvas, circleCenter, circleRadius);
  }

  /// 绘制标记形状
  void _drawMarkerShape(
    Canvas canvas,
    Offset circleCenter,
    double radius,
    Color baseColor,
  ) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..color = baseColor;

    // 创建完整的标记路径（圆形+尖点）
    final markerPath = _createMarkerPath(circleCenter, radius);
    canvas.drawPath(markerPath, paint);
  }

  /// 创建标记路径（圆形+尖点）
  Path _createMarkerPath(Offset circleCenter, double radius) {
    final path = Path();

    // 添加圆形
    path.addOval(Rect.fromCircle(center: circleCenter, radius: radius));

    // 添加尖点三角形
    final triangleHeight = size * 0.25;
    final triangleWidth = size * 0.12;
    final triangleTop = circleCenter.dy + radius;
    final triangleBottom = triangleTop + triangleHeight;

    final trianglePath = Path();
    trianglePath.moveTo(circleCenter.dx - triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx + triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx, triangleBottom);
    trianglePath.close();

    path.addPath(trianglePath, Offset.zero);

    return path;
  }

  /// 绘制轻微的立体效果
  void _drawSubtle3DEffect(
    Canvas canvas,
    Offset circleCenter,
    double radius,
    Color baseColor,
  ) {
    // 轻微的高光效果（左上角）
    final highlightPaint =
        Paint()
          ..style = PaintingStyle.fill
          ..shader = RadialGradient(
            center: const Alignment(-0.4, -0.4),
            radius: 0.5,
            colors: [
              Colors.white.withOpacity(0.15),
              Colors.white.withOpacity(0.05),
              Colors.transparent,
            ],
            stops: const [0.0, 0.6, 1.0],
          ).createShader(Rect.fromCircle(center: circleCenter, radius: radius));

    canvas.drawCircle(circleCenter, radius, highlightPaint);
  }

  /// 绘制简洁边框
  void _drawCleanBorder(Canvas canvas, Offset circleCenter, double radius) {
    // 单一边框，颜色根据背景自适应
    final borderPaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..color = Colors.white.withOpacity(0.9)
          ..strokeWidth = 1.5;

    // 绘制圆形边框
    canvas.drawCircle(circleCenter, radius, borderPaint);

    // 绘制尖点边框
    final triangleHeight = size * 0.25;
    final triangleWidth = size * 0.12;
    final triangleTop = circleCenter.dy + radius;
    final triangleBottom = triangleTop + triangleHeight;

    final trianglePath = Path();
    trianglePath.moveTo(circleCenter.dx - triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx + triangleWidth, triangleTop);
    trianglePath.lineTo(circleCenter.dx, triangleBottom);

    canvas.drawPath(trianglePath, borderPaint);
  }

  /// 根据点赞数量计算背景颜色
  /// 从浅灰色(0赞)渐变到红色(50+赞)，经过橙色和黄色
  Color _getBackgroundColor(int likes) {
    if (likes <= 0) {
      return const Color(0xFFF5F5F5); // 浅灰色，更好的起始颜色
    }

    // 将点赞数映射到0-1的范围，最大值设为50
    final normalizedLikes = math.min(likes / 50.0, 1.0);

    // 创建更自然的颜色渐变：浅灰 -> 浅蓝 -> 绿 -> 黄 -> 橙 -> 红
    if (normalizedLikes < 0.2) {
      // 浅灰到浅蓝
      final t = normalizedLikes / 0.2;
      return Color.lerp(
        const Color(0xFFF5F5F5), // 浅灰
        const Color(0xFFE3F2FD), // 浅蓝
        t,
      )!;
    } else if (normalizedLikes < 0.4) {
      // 浅蓝到绿
      final t = (normalizedLikes - 0.2) / 0.2;
      return Color.lerp(
        const Color(0xFFE3F2FD), // 浅蓝
        const Color(0xFFE8F5E8), // 浅绿
        t,
      )!;
    } else if (normalizedLikes < 0.6) {
      // 绿到黄
      final t = (normalizedLikes - 0.4) / 0.2;
      return Color.lerp(
        const Color(0xFFE8F5E8), // 浅绿
        const Color(0xFFFFF3E0), // 浅黄
        t,
      )!;
    } else if (normalizedLikes < 0.8) {
      // 黄到橙
      final t = (normalizedLikes - 0.6) / 0.2;
      return Color.lerp(
        const Color(0xFFFFF3E0), // 浅黄
        const Color(0xFFFFE0B2), // 浅橙
        t,
      )!;
    } else {
      // 橙到红
      final t = (normalizedLikes - 0.8) / 0.2;
      return Color.lerp(
        const Color(0xFFFFE0B2), // 浅橙
        const Color(0xFFFFEBEE), // 浅红
        t,
      )!;
    }
  }

  /// 绘制emoji
  void _drawEmoji(Canvas canvas, Offset circleCenter, double circleRadius) {
    // 创建文本画笔
    final textPainter = TextPainter(
      text: TextSpan(
        text: spot.displayFishEmoji,
        style: TextStyle(
          fontSize: circleRadius * 1.5, // emoji大小相对于圆形
          height: 1.0,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // 计算emoji的绘制位置（居中）
    final emojiOffset = Offset(
      circleCenter.dx - textPainter.width / 2,
      circleCenter.dy - textPainter.height / 2,
    );

    textPainter.paint(canvas, emojiOffset);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is _FishingSpotMarkerPainter) {
      return oldDelegate.likesCount != likesCount || oldDelegate.size != size;
    }
    return true;
  }
}

/// 钓点标记构建器
///
/// 用于在地图上创建钓点标记，自动获取点赞数量
class FishingSpotMarkerBuilder {
  /// 创建钓点标记
  ///
  /// [spot] 钓点数据
  /// [onTap] 点击回调
  /// [size] 标记大小
  /// [getLikesCount] 获取点赞数量的异步函数
  static Widget buildMarker({
    required FishingSpot spot,
    VoidCallback? onTap,
    double size = 60.0,
    Future<int> Function(String spotId)? getLikesCount,
  }) {
    return FutureBuilder<int>(
      future: getLikesCount?.call(spot.id) ?? Future.value(0),
      builder: (context, snapshot) {
        final likesCount = snapshot.data ?? 0;
        return FishingSpotMarker(
          spot: spot,
          likesCount: likesCount,
          onTap: onTap,
          size: size,
        );
      },
    );
  }
}
